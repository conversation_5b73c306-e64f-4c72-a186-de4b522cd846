"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.getCardActionAreaUtilityClass = getCardActionAreaUtilityClass;
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
function getCardActionAreaUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiCardActionArea', slot);
}
const cardActionAreaClasses = (0, _generateUtilityClasses.default)('MuiCardActionArea', ['root', 'focusVisible', 'focusHighlight']);
var _default = exports.default = cardActionAreaClasses;