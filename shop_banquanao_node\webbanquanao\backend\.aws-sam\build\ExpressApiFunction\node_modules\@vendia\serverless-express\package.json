{"name": "@vendia/serverless-express", "version": "4.12.6", "description": "This library enables you to utilize AWS Lambda and Amazon API Gateway to respond to web and API requests using your existing Node.js application framework.", "keywords": ["aws", "serverless", "api gateway", "lambda", "express", "koa", "hapi", "sails", "node.js", "http"], "homepage": "https://github.com/vendia/serverless-express", "bugs": {"url": "https://github.com/vendia/serverless-express/issues"}, "license": "Apache-2.0", "files": ["src/"], "main": "src/index.js", "repository": {"type": "git", "url": "https://github.com/vendia/serverless-express.git"}, "engines": {"node": ">=12"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test", "commit-msg": "commitlint -e $GIT_PARAMS"}}, "lint-staged": {"*.js": ["eslint --fix"]}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-max-line-length": [2, "always", 240]}}, "eslintIgnore": ["examples/*/node_modules"], "jest": {"collectCoverageFrom": ["src/**"], "testPathIgnorePatterns": ["examples/nestjs", "examples/basic-starter-api-gateway-v2-typescript"]}, "devDependencies": {"@commitlint/config-conventional": "^11.0.0", "@ryansonshine/commitizen": "^4.2.8", "@ryansonshine/cz-conventional-changelog": "^3.3.4", "@semantic-release/changelog": "^6.0.1", "@semantic-release/commit-analyzer": "^9.0.2", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.4", "@semantic-release/npm": "^9.0.1", "@semantic-release/release-notes-generator": "^10.0.3", "@types/aws-lambda": "^8.10.76", "@types/jest": "^26.0.20", "acorn": "^8.2.4", "commitlint": "^11.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "ejs": "^3.1.6", "eslint": "^7.26.0", "eslint-config-standard": "^16.0.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "express": "^4.17.1", "husky": "^4.3.8", "jest": "^27.2.4", "lint-staged": "^10.5.4", "node-notifier": "^9.0.1", "semantic-release": "^19.0.2", "typescript": "^4.2.4"}, "scripts": {"test": "jest && npm run tsc", "tsc": "tsc ./src/*.ts --outDir typescript-out", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cz": "git-cz", "release": "semantic-release", "release-local": "node -r dotenv/config node_modules/semantic-release/bin/semantic-release --no-ci --dry-run", "check-dependencies": "npx npm-check --skip-unused --update", "lint": "eslint src"}, "config": {"commitizen": {"path": "./node_modules/@ryansonshine/cz-conventional-changelog"}}, "dependencies": {"@codegenie/serverless-express": "^4.12.5"}}