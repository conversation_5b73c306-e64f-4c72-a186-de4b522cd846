{"pagination": {"ListAliases": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListAvailabilityConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AvailabilityConfigurations"}, "ListGroupMembers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListGroupsForEntity": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListImpersonationRoles": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMailDomains": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMailboxExportJobs": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMailboxPermissions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListMobileDeviceAccessOverrides": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListOrganizations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListResourceDelegates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListResources": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}, "ListUsers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults"}}}