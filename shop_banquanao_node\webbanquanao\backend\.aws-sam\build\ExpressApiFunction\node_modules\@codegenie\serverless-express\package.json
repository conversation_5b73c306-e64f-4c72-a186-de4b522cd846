{"name": "@codegenie/serverless-express", "version": "4.16.0", "description": "This library enables you to utilize AWS Lambda and Amazon API Gateway to respond to web and API requests using your existing Node.js application framework.", "keywords": ["aws", "serverless", "api gateway", "lambda", "express", "koa", "hapi", "sails", "node.js", "http"], "homepage": "https://github.com/CodeGenieApp/serverless-express", "bugs": {"url": "https://github.com/CodeGenieApp/serverless-express/issues"}, "license": "Apache-2.0", "files": ["src/"], "main": "src/index.js", "repository": {"type": "git", "url": "https://github.com/CodeGenieApp/serverless-express.git"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "lint-staged": {"*.js": ["eslint --fix"]}, "eslintIgnore": ["examples/*/node_modules"], "jest": {"collectCoverageFrom": ["src/**"], "testPathIgnorePatterns": ["examples/nestjs", "examples/basic-starter-api-gateway-v2-typescript", "examples/lambda-function-url"]}, "devDependencies": {"@commitlint/config-conventional": "^18.4.3", "@ryansonshine/commitizen": "^4.2.8", "@ryansonshine/cz-conventional-changelog": "^3.3.4", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^9.2.5", "@semantic-release/npm": "^11.0.2", "@semantic-release/release-notes-generator": "^12.1.0", "@types/aws-lambda": "^8.10.130", "@types/jest": "^29.5.11", "acorn": "^8.11.2", "commitlint": "^18.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "ejs": "^3.1.10", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "express": "^5.0.1", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^12.5.0", "node-notifier": "^10.0.1", "semantic-release": "^22.0.12", "typescript": "^5.3.3"}, "scripts": {"test": "jest", "verify-typescript-types": "tsc ./src/*.ts --outDir typescript-out", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cz": "git-cz", "release": "semantic-release", "release-local": "node -r dotenv/config node_modules/semantic-release/bin/semantic-release --no-ci --dry-run", "check-dependencies": "npx npm-check --skip-unused --update", "lint": "eslint src", "prepare": "husky install", "lint-staged": "lint-staged"}, "config": {"commitizen": {"path": "./node_modules/@ryansonshine/cz-conventional-changelog"}}}